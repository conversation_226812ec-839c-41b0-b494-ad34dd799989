<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="keywords" content="METAQIU, METAQIU的球球空间" />
    <meta name="description" content="METAQIU的球球空间，分享记录日常生活中的所遇。" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimum-scale=1.0, maximum-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <!-- favicon -->
    <link rel="icon" href="./metaqiu.webp" type="image/webp">
    <!-- title -->
    <title>METAQIU'S HOME</title>
    <!-- css -->
    <link href="./css/style.css" rel="stylesheet" type="text/css" />
    <!-- style -->
    <style>
            h1::before{
                content: 'METAQIU\'S HOME';
                position: absolute;
                color: #b2bec348;
                z-index: -1;
                left: 10px;
                top: 10px;
            }
    
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>
    <div class="background-area">
        <img class="lazy-img" data-original="./images/wallhaven-8xd6p1.jpg" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAEALAAAAAABAAEAAAICRAEAOw==">
    </div>

    <div class="main">
        
        <div class="title animate">
            <h1>METAQIU'S HOME</h1>
        </div>
        <br>
        <br>
        <div class="content">
            <p class="animate" style="font-size: 1.2rem; line-height: 10px;">人生就是一个故事。</p>
            <br>
            <p class="animate" style="font-size: 1.2rem; line-height: 10px;">所以，请享受生命，热爱生活吧！</p>
            <br>
            <p class="animate" style="font-size: 1.2rem; line-height: 10px;">" 艺术家创造美，而不是接受现成的美 " &nbsp— &nbsp歌德</p>
            
            <div class="intro-section animate">
                <p class="intro-text">👋 你好，我是 MetaQiu</p>
                <p class="intro-detail">🎨 热爱设计与编程的创作者</p>
                <p class="intro-detail">💡 正在探索有趣的数字世界</p>
                <p class="intro-detail">🌱 永远保持学习的热情</p>
                <p class="intro-detail">🌈 什么都不会的笨比</p>
            </div>
        </div>

        <div class="social-links animate">
            <a href="https://github.com/metaqiu" target="_blank" class="social-item">
                <i class="fab fa-github"></i>
            </a>
            <a href="mailto:<EMAIL>" class="social-item">
                <i class="fas fa-envelope"></i>
            </a>
            <a href="https://twitter.com/" target="_blank" class="social-item">
                <i class="fab fa-twitter"></i>
            </a>
        </div>

        <div class="footer">
            <!-- New Footer -->
            <a class="Copyright"><span>Copyright © 2024 - <script>document.write(new Date().getFullYear());</script> METAQIU All Rights Reserved.</span></a>
                <br>
            <a class="test-decoration">In the next two years, unlimited imagination and miracles.</a>
                <br>
            本网站上的所有内容（包括但不限于文章、图像）
            <br>
            根据<a href="http://creativecommons.org/licenses/by-nc-sa/4.0/" rel="license">知识共享署名-非商业性使用-相同方式共享 4.0 国际许可协议</a>进行许可。
            <br>
            <span id="runtime">本站已运行: 0天0小时0分0秒</span>
        </div>

    </div>

    <script src="./js/jquery.min.js"></script>
    <script src="./js/jquery.lazyload.min.js"></script>
    <script>
        // lazyload
        window.onload = function() {
            $('.background-area img').addClass('lazyload');
            $(function() {
                $("img.lazyload").lazyload({
                    effect: 'fadeIn',
                    effectspeed: 1000
                });
            });

            $(".background-area img").lazyload({
                effect: 'fadeIn',
                effectspeed: 1000 
            });
        }

        // 添加运行时间统计
        function showRuntime() {
            const startTime = new Date("2024/01/01 00:00:00"); 
            const currentTime = new Date();
            const runTime = currentTime - startTime;
            
            const day = Math.floor(runTime / (24 * 60 * 60 * 1000));
            const hour = Math.floor((runTime % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000));
            const minute = Math.floor((runTime % (60 * 60 * 1000)) / (60 * 1000));
            const second = Math.floor((runTime % (60 * 1000)) / 1000);
            
            document.getElementById("runtime").innerHTML = 
                `本站已运行: ${day}天${hour}小时${minute}分${second}秒`;
        }

        // 每秒更新一次
        setInterval(showRuntime, 1000);
        showRuntime();
    </script>
</body>
</html>
